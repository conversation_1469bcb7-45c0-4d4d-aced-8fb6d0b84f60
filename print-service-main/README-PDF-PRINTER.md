# PDF Printer Service - Go Implementation

This document describes the Go implementation of the PDF generation service that mirrors the TypeScript pdf-service functionality.

## Overview

The `pdf-printer.go` module provides a Go port of the TypeScript PDF service, allowing generation of structured PDF reports from JSON data. This implementation:

- **Preserves Go patterns** from the existing `main.go` (Fiber framework, middleware, error handling)
- **Mirrors TypeScript I/O structure** with equivalent DTOs and interfaces
- **Uses native Go PDF generation** with `gofpdf` library (no browser dependencies)
- **Provides standard configuration** with sensible defaults when input params are empty
- **Maintains API compatibility** with the TypeScript service

## Architecture

### Input/Output DTOs

The Go implementation mirrors the TypeScript service DTOs:

**TypeScript (pdf-service)**:
```typescript
interface ReportDocumentProps {
  sections: ReportSection[];
  metadata: ReportMetadata;
}
```

**Go (pdf-printer)**:
```go
type ReportDocumentRequest struct {
  Sections []ReportSection `json:"sections"`
  Metadata ReportMetadata  `json:"metadata"`
}
```

### Key Structures

```go
type ReportSection struct {
  Title      string                   `json:"title"`
  Subtitle   string                   `json:"subtitle"`
  Subsection string                   `json:"subsection"`
  Source     []string                 `json:"source"`
  DataCount  int                      `json:"data_count"`
  IsDeleted  *bool                    `json:"is_deleted,omitempty"`
  Data       []map[string]interface{} `json:"data"`
}

type ReportMetadata struct {
  ReportID         string                 `json:"report_id"`
  ReportStatus     string                 `json:"report_status"`
  ReportType       string                 `json:"report_type"`
  ReportSearchArgs map[string]interface{} `json:"report_search_args"`
  ReportName       string                 `json:"report_name"`
  CreationAt       string                 `json:"creation_at"`
  ModifiedAt       string                 `json:"modified_at"`
  SubjectName      string                 `json:"subject_name"`
  SubjectMotherName string                `json:"subject_mother_name"`
  SubjectAge       *int                   `json:"subject_age"`
  SubjectSex       string                 `json:"subject_sex"`
}
```

## API Endpoints

### New Endpoint: `/pdf/generate`

**Method**: `POST`  
**Content-Type**: `application/json`  
**Authorization**: `Bearer <token>` (required)

**Request Body**: Same structure as TypeScript service
```json
{
  "sections": [...],
  "metadata": {...}
}
```

**Response**: 
- **Content-Type**: `application/pdf`
- **Content-Disposition**: `attachment; filename="report_name.pdf"`
- **Body**: PDF binary data

### Existing Endpoint: `/pdf-base64`

The original HTML-to-PDF endpoint remains unchanged for backward compatibility.

## Features

### PDF Generation
- **Header**: Report title, type, search parameters, subject information
- **Summary Page**: Overview of all sections with data counts
- **Section Pages**: Detailed data tables for each section
- **Pagination**: Automatic page breaks and section distribution
- **Data Limiting**: Shows first 10 rows per section with "more records" indicator
- **Column Limiting**: Shows first 5 columns per table to fit page width

### Configuration
- **Default Config**: Sensible defaults when no config provided
- **A4 Portrait**: Standard page size and orientation
- **Margins**: Consistent spacing (20mm top/bottom, 15mm left/right)
- **Fonts**: Arial family with appropriate sizing
- **Auto Page Break**: Automatic pagination

### Error Handling
- **Input Validation**: Validates required fields (sections, metadata)
- **Structured Errors**: JSON error responses with appropriate HTTP status codes
- **Logging**: Comprehensive request/response logging matching TypeScript service

## Usage

### 1. Start the Server
```bash
go run .
```

### 2. Test the Endpoint

**Using curl**:
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-access-key" \
  -d @test-report-payload.json \
  http://localhost:80/pdf/generate \
  --output report.pdf
```

**Using PowerShell**:
```powershell
.\test-pdf-generate.ps1
```

**Using Bash**:
```bash
./test-pdf-generate.sh
```

### 3. Example Request

See `test-report-payload.json` for a complete example with:
- Multiple sections (Dados Pessoais, Telefones, Emails, Endereços)
- Realistic metadata
- Sample data arrays

## Dependencies

The implementation adds one new dependency:
- `github.com/jung-kurt/gofpdf` - Pure Go PDF generation library

All existing dependencies remain unchanged.

## Comparison with TypeScript Service

| Feature | TypeScript Service | Go Service |
|---------|-------------------|------------|
| Framework | Express.js | Fiber |
| PDF Library | @react-pdf/renderer | gofpdf |
| Output Format | PDF Stream | PDF Binary |
| Browser Dependency | Yes (React rendering) | No |
| Memory Usage | Higher (React components) | Lower (direct PDF generation) |
| Performance | Slower (component rendering) | Faster (direct generation) |
| Styling | CSS-like styling | Programmatic styling |

## Benefits of Go Implementation

1. **No Browser Dependencies**: Eliminates ChromeDP/Puppeteer overhead
2. **Better Performance**: Direct PDF generation without HTML rendering
3. **Lower Memory Usage**: No React component tree or browser process
4. **Simpler Deployment**: Single binary with no external dependencies
5. **Better Resource Control**: Predictable memory and CPU usage
6. **Consistent Output**: No browser-specific rendering differences

## Future Enhancements

- **Custom Styling**: Support for custom fonts, colors, and layouts
- **Advanced Tables**: Better table formatting and column management
- **Charts/Graphics**: Integration with Go charting libraries
- **Template System**: Configurable report templates
- **Streaming**: Large report streaming for memory efficiency
- **Caching**: PDF caching for repeated requests
