import React from 'react';
import { View, Text, StyleSheet } from '@react-pdf/renderer';
import { ReportMetadata } from '../types/global';
import { getInitials } from '../helpers';
import { REPORT_CONSTANTS } from '../config/constants';

interface PrintProfileHeaderProps {
  metadata: ReportMetadata;
  report_type: string;
  searchValue: string;
}

export const PrintProfileHeader: React.FC<PrintProfileHeaderProps> = ({ metadata, report_type, searchValue }) => {
  const profileName = metadata[REPORT_CONSTANTS.new_report.subject_name] as string || 'Nome não disponível';
  const initials = getInitials(profileName as string);
  const idade = metadata[REPORT_CONSTANTS.new_report.subject_age] as number | string ?? 'N/A';
  const sexo = metadata[REPORT_CONSTANTS.new_report.subject_sex] as string || 'N/A';
  const nomeMae = metadata[REPORT_CONSTANTS.new_report.subject_mother_name] as string || 'N/A';

  const dataItems = [
    { label: 'NOME', value: profileName },
    { label: 'NOME DA MÃE', value: nomeMae },
    { label: 'IDADE', value: idade },
    { label: 'SEXO', value: sexo }
  ];

  const midpoint = Math.ceil(dataItems.length / 2);
  const firstColumn = dataItems.slice(0, midpoint);
  const secondColumn = dataItems.slice(midpoint);

  const renderDataColumn = (columnItems: { label: string; value: string | number }[]) => (
    <View style={styles.dataColumn}>
      {columnItems.map((item, idx) => (
        <View key={idx} style={styles.dataRow}>
          <Text style={styles.dataLabel}>{item.label}</Text>
          <Text style={styles.dataValue}>{item.value}</Text>
        </View>
      ))}
    </View>
  );

  return (
    <View style={styles.headerContainer}>
      <View style={styles.topRow}>
        <View style={styles.profileHeaderSection}>
          <Text style={styles.sectionHeader}>PERFIL</Text>
          <View style={styles.photoContainer}>
            <View style={styles.avatarBox}>
              <Text style={styles.avatarFallback}>{initials}</Text>
            </View>
          </View>
        </View>

        <View style={styles.entradasSection}>
          <Text style={styles.sectionHeader}>ENTRADAS</Text>
          <View style={styles.entryContainer}>
            <Text style={styles.dataLabel}>{report_type}</Text>
            <View style={styles.entryBox}>
              <Text style={styles.dataValue}>{searchValue}</Text>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.dataListContainer}>
        {renderDataColumn(firstColumn)}
        {renderDataColumn(secondColumn)}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    width: "100%",
    flexDirection: "column",
  },
  topRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  profileHeaderSection: {
    width: "48%",
    paddingRight: 16,
  },
  entradasSection: {
    width: "48%",
  },
  dataListContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  dataColumn: {
    width: "48%",
  },
  sectionHeader: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#FE473C",
    marginBottom: 16,
    letterSpacing: 0.5,
  },
  photoContainer: {
    alignItems: "center",
    marginBottom: 20,
  },
  avatarBox: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#E5E7EB",
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  avatarFallback: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#6B7280",
    textAlign: "center",
  },
  dataRow: {
    marginBottom: 12,
    paddingBottom: 8,
    borderBottom: "1px solid #E5E7EB",
  },
  dataLabel: {
    fontSize: 8,
    fontWeight: "bold",
    color: "#FE473C",
    marginBottom: 4,
    letterSpacing: 0.5,
    textTransform: "uppercase",
  },
  dataValue: {
    fontSize: 10,
    fontWeight: "normal",
  },
  entryContainer: {
    padding: 8,
    backgroundColor: "#F9F9FA",
    borderRadius: 4,
  },
  entryBox: {
    borderTopWidth: 1,
    borderColor: "#E5E7EB",
    paddingVertical: 4,
  },
});
