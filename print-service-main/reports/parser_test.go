package reports

import (
	"encoding/json"
	"os"
	"testing"
)

func TestParseMuitaTreta(t *testing.T) {
	// Load test data
	inputJSON, err := os.ReadFile("../muita_treta.json")
	if err != nil {
		t.Fatalf("Failed to read test input file: %v", err)
	}

	// Parse the input
	outputJSON, err := ParseMuitaTreta(inputJSON)
	if err != nil {
		t.Fatalf("ParseMuitaTreta failed: %v", err)
	}

	// Validate the output
	var output ReportDocumentRequest
	if err := json.Unmarshal(outputJSON, &output); err != nil {
		t.Fatalf("Failed to unmarshal output JSON: %v", err)
	}

	// Check that we have sections
	if len(output.Sections) == 0 {
		t.Error("Expected sections in output, got none")
	}

	// Check that metadata is populated
	if output.Metadata.ReportName == "" {
		t.<PERSON>rror("Expected report name in metadata, got empty string")
	}

	if output.Metadata.ReportType == "" {
		t.Error("Expected report type in metadata, got empty string")
	}

	// Print some info about the parsed data
	t.Logf("Successfully parsed muita_treta.json")
	t.Logf("Report name: %s", output.Metadata.ReportName)
	t.Logf("Report type: %s", output.Metadata.ReportType)
	t.Logf("Number of sections: %d", len(output.Sections))

	// Optional: Save the output to a file for manual inspection
	// This can be helpful for debugging
	if err := os.WriteFile("../test_parser_output.json", outputJSON, 0644); err != nil {
		t.Logf("Warning: Failed to write test output file: %v", err)
	}
}
