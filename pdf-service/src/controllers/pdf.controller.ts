import { Request, Response, NextFunction } from 'express';
import { createElement } from 'react';
import { renderToStream } from '@react-pdf/renderer';
import { ReportDocument } from '../components/ReportDocument';
import { AppError } from '../middlewares/error.middleware';

export interface ReportDocumentProps {
  sections: any[];
  metadata: any;
}

export const generatePDF = async (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();

  try {
    const { sections, metadata }: ReportDocumentProps = req.body;

    // Validate input
    if (!sections || !metadata) {
      const error = new Error('Missing required fields: sections and metadata') as AppError;
      error.statusCode = 400;
      error.isOperational = true;
      throw error;
    }

    if (!Array.isArray(sections)) {
      const error = new Error('Sections must be an array') as AppError;
      error.statusCode = 400;
      error.isOperational = true;
      throw error;
    }

    console.log('=== PDF Generation Request ===');
    console.log('Report name:', metadata.report_name || 'Unnamed Report');
    console.log('Report type:', metadata.report_type || 'Unknown');
    console.log('Sections count:', sections.length);
    console.log('Request size:', JSON.stringify(req.body).length, 'bytes');

    // Generate PDF using @react-pdf/renderer
    console.log('Starting PDF generation...');
    const pdfGenerationStart = Date.now();

    const pdfStream = await renderToStream(createElement(ReportDocument, { sections, metadata }) as any);

    console.log('PDF generation completed in', Date.now() - pdfGenerationStart, 'ms');

    // Set response headers for PDF
    const filename = `${metadata.report_name || 'report'}.pdf`.replace(/[^a-zA-Z0-9.-]/g, '_');
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');

    pdfStream.pipe(res);

    const totalTime = Date.now() - startTime;
    console.log('=== PDF Generation Complete ===');
    console.log('Total processing time:', totalTime, 'ms');
    console.log('================================');
    
  } catch (error) {
    const totalTime = Date.now() - startTime;
    console.error('=== PDF Generation Failed ===');
    console.error('Error after', totalTime, 'ms:', error);
    console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace');
    console.error('==============================');
    // Detectar erro maldito de memória
    if (
      error instanceof Error &&
      /allocation failed|heap out of memory/i.test(error.message)
    ) {
      console.error('Erro de heap - saindo para reiniciar o container...');

      setTimeout(() => process.exit(1), 100);
      return;
    }

    const appError = (error as AppError) || new Error('Unknown PDF error');
    appError.statusCode = appError.statusCode || 500;
    appError.isOperational = true;
    return next(appError);
  }
};
