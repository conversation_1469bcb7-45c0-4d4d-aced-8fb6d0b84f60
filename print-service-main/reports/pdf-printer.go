package reports

import (
	"bytes"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/jung-kurt/gofpdf"
)

// ReportType represents the type of report being generated
type ReportType string

const (
	ReportTypeCPF      ReportType = "cpf"
	ReportTypeCNPJ     ReportType = "cnpj"
	ReportTypeEmail    ReportType = "email"
	ReportTypeTelefone ReportType = "telefone"
)

// ReportSection represents a section of the report
type ReportSection struct {
	Title      string                   `json:"title"`
	Subtitle   string                   `json:"subtitle"`
	Subsection string                   `json:"subsection"`
	Source     []string                 `json:"source"`
	DataCount  int                      `json:"data_count"`
	IsDeleted  *bool                    `json:"is_deleted,omitempty"`
	Data       []map[string]interface{} `json:"data"`
}

// ReportMetadata contains metadata about the report
type ReportMetadata struct {
	ReportID          string                 `json:"report_id"`
	ReportStatus      string                 `json:"report_status"`
	ReportType        string                 `json:"report_type"`
	ReportSearchArgs  map[string]interface{} `json:"report_search_args"`
	ReportName        string                 `json:"report_name"`
	CreationAt        string                 `json:"creation_at"`
	ModifiedAt        string                 `json:"modified_at"`
	SubjectName       string                 `json:"subject_name"`
	SubjectMotherName string                 `json:"subject_mother_name"`
	SubjectAge        *int                   `json:"subject_age"`
	SubjectSex        string                 `json:"subject_sex"`
}

// ReportDocumentRequest represents the input for PDF generation
type ReportDocumentRequest struct {
	Sections []ReportSection `json:"sections"`
	Metadata ReportMetadata  `json:"metadata"`
}

// PDFConfig holds default configuration for PDF generation
type PDFConfig struct {
	PageSize     string
	Orientation  string
	Unit         string
	FontFamily   string
	FontSize     float64
	MarginTop    float64
	MarginBottom float64
	MarginLeft   float64
	MarginRight  float64
}

// GetDefaultPDFConfig returns default PDF configuration
func GetDefaultPDFConfig() PDFConfig {
	return PDFConfig{
		PageSize:     "A4",
		Orientation:  "P", // Portrait
		Unit:         "mm",
		FontFamily:   "Arial",
		FontSize:     10,
		MarginTop:    20,
		MarginBottom: 20,
		MarginLeft:   15,
		MarginRight:  15,
	}
}

// GeneratePDF creates a PDF from the report data
func GeneratePDF(req ReportDocumentRequest, config PDFConfig) (*bytes.Buffer, error) {
	pdf := gofpdf.New(config.Orientation, config.Unit, config.PageSize, "")

	// Set margins
	pdf.SetMargins(config.MarginLeft, config.MarginTop, config.MarginRight)
	pdf.SetAutoPageBreak(true, config.MarginBottom)

	// Add first page
	pdf.AddPage()

	// Set font
	pdf.SetFont(config.FontFamily, "", config.FontSize)

	// Generate header
	generateHeader(pdf, req.Metadata, config)

	// Generate summary
	generateSummary(pdf, req.Sections, config)

	// Generate sections
	generateSections(pdf, req.Sections, req.Metadata, config)

	// Create buffer and write PDF
	var buf bytes.Buffer
	err := pdf.Output(&buf)
	if err != nil {
		return nil, fmt.Errorf("failed to generate PDF: %w", err)
	}

	return &buf, nil
}

// generateHeader creates the PDF header
func generateHeader(pdf *gofpdf.Fpdf, metadata ReportMetadata, config PDFConfig) {
	// Title
	pdf.SetFont(config.FontFamily, "B", 16)
	title := metadata.ReportName
	if title == "" {
		title = "Relatório"
	}
	pdf.Cell(0, 10, strings.ToUpper(title))
	pdf.Ln(15)

	// Report type and search value
	pdf.SetFont(config.FontFamily, "", 10)
	reportType := metadata.ReportType
	searchValue := getSearchValue(metadata.ReportSearchArgs)

	if reportType != "" && searchValue != "" {
		pdf.Cell(0, 8, fmt.Sprintf("Tipo: %s | Busca: %s", strings.ToUpper(reportType), searchValue))
		pdf.Ln(10)
	}

	// Subject information
	if metadata.SubjectName != "" {
		pdf.Cell(0, 6, fmt.Sprintf("Nome: %s", metadata.SubjectName))
		pdf.Ln(8)
	}

	// Generation date
	pdf.SetFont(config.FontFamily, "", 8)
	pdf.Cell(0, 6, fmt.Sprintf("Gerado em: %s", time.Now().Format("02/01/2006 15:04")))
	pdf.Ln(15)
}

// generateSummary creates a summary of all sections
func generateSummary(pdf *gofpdf.Fpdf, sections []ReportSection, config PDFConfig) {
	pdf.SetFont(config.FontFamily, "B", 12)
	pdf.Cell(0, 10, "RESUMO")
	pdf.Ln(12)

	pdf.SetFont(config.FontFamily, "", 9)

	// Filter printable sections (non-subsections with data)
	printableSections := filterPrintableSections(sections)

	for _, section := range printableSections {
		dataCount := section.DataCount
		if dataCount > 0 {
			pdf.Cell(0, 6, fmt.Sprintf("• %s: %d registro(s)", section.Title, dataCount))
			pdf.Ln(7)
		}
	}

	pdf.Ln(10)
}

// generateSections creates the detailed sections
func generateSections(pdf *gofpdf.Fpdf, sections []ReportSection, metadata ReportMetadata, config PDFConfig) {
	printableSections := filterPrintableSections(sections)

	for i, section := range printableSections {
		if i > 0 {
			pdf.AddPage()
		}

		generateSection(pdf, section, config)
	}
}

// generateSection creates a single section
func generateSection(pdf *gofpdf.Fpdf, section ReportSection, config PDFConfig) {
	// Section title
	pdf.SetFont(config.FontFamily, "B", 14)
	pdf.Cell(0, 10, strings.ToUpper(section.Title))
	pdf.Ln(12)

	// Data count
	pdf.SetFont(config.FontFamily, "", 9)
	pdf.Cell(0, 6, fmt.Sprintf("Total de registros: %d", section.DataCount))
	pdf.Ln(10)

	// Section data
	if len(section.Data) > 0 {
		generateSectionData(pdf, section.Data, config)
	} else {
		pdf.SetFont(config.FontFamily, "I", 9)
		pdf.Cell(0, 6, "Nenhum dado encontrado para esta seção.")
		pdf.Ln(8)
	}
}

// generateSectionData creates the data table for a section
func generateSectionData(pdf *gofpdf.Fpdf, data []map[string]interface{}, config PDFConfig) {
	if len(data) == 0 {
		return
	}

	// Get column headers from first data item
	var headers []string
	firstItem := data[0]
	for key := range firstItem {
		headers = append(headers, key)
	}

	// Limit to first 5 columns to fit on page
	if len(headers) > 5 {
		headers = headers[:5]
	}

	// Calculate column width
	pageWidth, _ := pdf.GetPageSize()
	margins := config.MarginLeft + config.MarginRight
	availableWidth := pageWidth - margins
	colWidth := availableWidth / float64(len(headers))

	// Table headers
	pdf.SetFont(config.FontFamily, "B", 8)
	for _, header := range headers {
		pdf.CellFormat(colWidth, 8, strings.Title(header), "1", 0, "C", false, 0, "")
	}
	pdf.Ln(-1)

	// Table data (limit to first 10 rows)
	pdf.SetFont(config.FontFamily, "", 7)
	maxRows := 10
	if len(data) < maxRows {
		maxRows = len(data)
	}

	for i := 0; i < maxRows; i++ {
		item := data[i]
		for _, header := range headers {
			value := ""
			if val, exists := item[header]; exists && val != nil {
				value = fmt.Sprintf("%v", val)
				// Truncate long values
				if len(value) > 25 {
					value = value[:22] + "..."
				}
			}
			pdf.CellFormat(colWidth, 6, value, "1", 0, "L", false, 0, "")
		}
		pdf.Ln(-1)
	}

	// Show if there are more records
	if len(data) > maxRows {
		pdf.Ln(5)
		pdf.SetFont(config.FontFamily, "I", 8)
		pdf.Cell(0, 6, fmt.Sprintf("... e mais %d registro(s)", len(data)-maxRows))
		pdf.Ln(8)
	}
}

// filterPrintableSections returns all sections without validation
func filterPrintableSections(sections []ReportSection) []ReportSection {
	return sections
}

// getSearchValue extracts the search value from report search args
func getSearchValue(searchArgs map[string]interface{}) string {
	for _, value := range searchArgs {
		if str, ok := value.(string); ok && str != "" {
			return str
		}
	}
	return ""
}

// GeneratePDFHandler handles the PDF generation endpoint
func GeneratePDFHandler(c *fiber.Ctx) error {
	startTime := time.Now()

	// Get request body
	var req ReportDocumentRequest

	// Parse request body
	if bodyErr := c.BodyParser(&req); bodyErr != nil {
		log.Printf("Error parsing request body: %v", bodyErr)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Log request info
	log.Printf("=== PDF Generation Request ===")
	log.Printf("Report name: %s", getReportName(req.Metadata))
	log.Printf("Report type: %s", req.Metadata.ReportType)
	log.Printf("Sections count: %d", len(req.Sections))
	log.Printf("Request processing started...")

	// Use default config (can be enhanced to accept config from request)
	config := GetDefaultPDFConfig()

	// Generate PDF
	pdfGenerationStart := time.Now()
	pdfBuffer, err := GeneratePDF(req, config)
	if err != nil {
		log.Printf("PDF generation failed: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to generate PDF",
		})
	}

	pdfGenerationTime := time.Since(pdfGenerationStart).Seconds()
	log.Printf("PDF generation completed in %.3fs", pdfGenerationTime)

	// Set response headers
	filename := sanitizeFilename(getReportName(req.Metadata)) + ".pdf"
	c.Set("Content-Type", "application/pdf")
	c.Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	c.Set("Cache-Control", "no-cache, no-store, must-revalidate")

	totalTime := time.Since(startTime).Seconds()
	log.Printf("=== PDF Generation Complete ===")
	log.Printf("Total processing time: %.3fs", totalTime)
	log.Printf("================================")

	return c.Send(pdfBuffer.Bytes())
}

// getReportName safely gets the report name with fallback
func getReportName(metadata ReportMetadata) string {
	if metadata.ReportName != "" {
		return metadata.ReportName
	}
	return "report"
}

// sanitizeFilename removes invalid characters from filename
func sanitizeFilename(filename string) string {
	// Replace invalid characters with underscore
	invalidChars := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|", " "}
	result := filename
	for _, char := range invalidChars {
		result = strings.ReplaceAll(result, char, "_")
	}
	return result
}
