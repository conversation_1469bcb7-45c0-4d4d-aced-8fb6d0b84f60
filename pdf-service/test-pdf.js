const fs = require('fs');
const path = require('path');

// Sample test data
const testData = {
  sections: [
    {
      title: "Emails",
      subtitle: "Informações de Email",
      subsection: "",
      source: ["api", "database"],
      data_count: 2,
      is_deleted: false,
      data: [
        {
          email: {
            value: "<EMAIL>",
            label: "Email Principal",
            source: ["api"],
            is_deleted: false
          },
          detalhes: {
            provedor: {
              value: "Gmail",
              label: "<PERSON><PERSON><PERSON>",
              source: ["api"],
              is_deleted: false
            },
            data_criacao: {
              value: "2023-01-15",
              label: "Data de Criação",
              source: ["database"],
              is_deleted: false
            }
          }
        }
      ]
    },
    {
      title: "Telefones",
      subtitle: "Informações de Telefone",
      subsection: "",
      source: ["api"],
      data_count: 1,
      is_deleted: false,
      data: [
        {
          numero: {
            value: "+55 11 99999-9999",
            label: "<PERSON><PERSON>mer<PERSON>",
            source: ["api"],
            is_deleted: false
          },
          detalhes: {
            operadora: {
              value: "Vivo",
              label: "Operadora",
              source: ["api"],
              is_deleted: false
            },
            tipo: {
              value: "<PERSON>óvel",
              label: "Tipo",
              source: ["api"],
              is_deleted: false
            }
          }
        }
      ]
    }
  ],
  metadata: {
    report_id: "test-123",
    report_status: "completed",
    report_type: "cpf",
    report_search_args: { cpf: "123.456.789-00" },
    report_name: "Relatório de Teste",
    creation_at: "2024-01-15T10:00:00Z",
    modified_at: "2024-01-15T10:30:00Z",
    subject_name: "João da Silva",
    subject_mother_name: "Maria da Silva",
    subject_age: 35,
    subject_sex: "Masculino"
  }
};

async function testPDFGeneration() {
  try {
    console.log('Testing PDF generation...');
    
    const response = await fetch('http://localhost:3002/api/pdf/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    console.log('PDF generation successful!');
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    // Save the PDF to a file
    const pdfBuffer = await response.arrayBuffer();
    const outputPath = path.join(__dirname, 'test-output.pdf');
    
    fs.writeFileSync(outputPath, Buffer.from(pdfBuffer));
    console.log(`PDF saved to: ${outputPath}`);
    console.log(`PDF size: ${pdfBuffer.byteLength} bytes`);
    
  } catch (error) {
    console.error('Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testPDFGeneration();
