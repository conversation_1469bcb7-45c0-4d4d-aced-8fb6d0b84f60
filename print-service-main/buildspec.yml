version: 0.2

phases:
  pre_build:
    commands:
      - echo Entered the pre_build phase...
      - echo `aws --version`
      - pip3 install --upgrade pip
      - pip3 install awscli --upgrade --user
      - echo Logging in to Amazon ECR...
      - docker login -u $DOCKER_HUB_USERNAME -p $DOCKER_HUB_TOKEN
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $REPOSITORY_URL
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build -t $IMAGE_REPO_NAME .
      - docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $REPOSITORY_URL
      - echo Pushing the Docker image...
      - docker push $REPOSITORY_URL
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Writing image definitions file...
      - printf '[{"name":"%s","imageUri":"%s"}]' $CONTAINER_NAME $REPOSITORY_URL > imagedefinitions.json
artifacts:
  files: imagedefinitions.json
