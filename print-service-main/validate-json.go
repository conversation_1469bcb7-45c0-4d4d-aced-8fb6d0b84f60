package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
)

func main() {
	// Read the JSON file
	jsonData, err := ioutil.ReadFile("muita_treta.json")
	if err != nil {
		fmt.Printf("Error reading file: %v\n", err)
		os.Exit(1)
	}

	// Try to parse the JSON
	var result interface{}
	err = json.Unmarshal(jsonData, &result)
	if err != nil {
		fmt.Printf("Error parsing JSON: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("JSON is valid!")
}