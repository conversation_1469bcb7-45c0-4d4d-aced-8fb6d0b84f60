import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '@react-pdf/renderer';
import { ReportSection, ValueWithSource } from '../types/global';
import { parseValue, translatePropToLabel, translateSource } from '../helpers';

interface RenderPrintDadosPessoaisProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      detalhes?: Record<string, ValueWithSource>;
    }>;
  };
}

export const RenderPrintDadosPessoais: React.FC<RenderPrintDadosPessoaisProps> = ({ section }) => {
  const entry = section.data?.[0]?.detalhes ?? {};
  const items = Object.entries(entry).filter(
    ([, v]) => typeof v.value === 'string' && v.is_deleted === false
  ) as Array<[string, ValueWithSource<string>]>

  return (
    <View style={styles.container} key={section.title}>
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>
      <View style={styles.grid}>
        {items.map(([key, { label, value, source }], idx) => (
          <View key={idx} style={styles.cell} wrap={false}>
            <View style={styles.infoContainer}>
              <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                <Rect width="8" height="8" fill='#CCCCCC' />
              </Svg>
              <Text style={styles.label}>{translatePropToLabel(label).toUpperCase()}</Text>
              <Text style={styles.sourceText}>
                {source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
              </Text>
            </View>
            <Text style={styles.value}>{parseValue(value)}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  grid: {
    paddingLeft: 8,
    paddingTop: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  cell: {
    width: '50%', // duas colunas
    paddingRight: 8,
    marginBottom: 8,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  searchIcon: { width: 4, height: 4, marginRight: 4, marginTop: 1 },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
});
