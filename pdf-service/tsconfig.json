{"compilerOptions": {"target": "ES2020", "module": "commonjs", "moduleResolution": "node", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "jsx": "react-jsx", "allowSyntheticDefaultImports": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "ts-node": {"files": true}}