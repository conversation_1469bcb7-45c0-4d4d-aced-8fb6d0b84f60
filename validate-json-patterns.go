package main

import (
	"fmt"
	"io/ioutil"
	"os"
	"regexp"
	"strings"
)

func main() {
	// Read the JSON file
	jsonData, err := ioutil.ReadFile("reports-pfd-data.json")
	if err != nil {
		fmt.Printf("Error reading file: %v\n", err)
		os.Exit(1)
	}

	// Convert to string for easier manipulation
	jsonString := string(jsonData)

	// Check for common JSON syntax errors
	checkPattern(jsonString, ",]", "Comma before closing bracket")
	checkPattern(jsonString, ",}", "Comma before closing brace")
	checkPattern(jsonString, "\\[\\s*]", "Empty array with whitespace")
	checkPattern(jsonString, "\\{\\s*}", "Empty object with whitespace")
	checkPattern(jsonString, ":\\s*,", "Colon followed by comma")
	checkPattern(jsonString, "\\[\\s*,", "Opening bracket followed by comma")
	checkPattern(jsonString, "\\{\\s*,", "Opening brace followed by comma")
	checkPattern(jsonString, "]\\s*[^,\\]\\}\\s]", "Closing bracket not followed by comma, bracket, or brace")
	checkPattern(jsonString, "}\\s*[^,\\]\\}\\s]", "Closing brace not followed by comma, bracket, or brace")
	checkPattern(jsonString, "\"\\s*\"", "Empty string with whitespace")
	checkPattern(jsonString, "\"[^\"]*$", "Unclosed string")
	checkPattern(jsonString, "\\\\[^\"\\\\bfnrtu]", "Invalid escape sequence")

	// Check for the specific error mentioned in the issue
	checkPattern(jsonString, "]\\s*r", "Closing bracket followed by 'r'")
	checkPattern(jsonString, "],\\s*r", "Closing bracket, comma, followed by 'r'")

	fmt.Println("Validation completed!")
}

func checkPattern(jsonString, pattern, description string) {
	re := regexp.MustCompile(pattern)
	matches := re.FindAllStringIndex(jsonString, -1)

	if len(matches) > 0 {
		fmt.Printf("Found %d instances of %s:\n", len(matches), description)

		for i, match := range matches {
			if i >= 5 {
				fmt.Printf("... and %d more\n", len(matches)-5)
				break
			}

			start := match[0]
			end := match[1]

			// Get some context around the match
			contextStart := start - 20
			if contextStart < 0 {
				contextStart = 0
			}
			contextEnd := end + 20
			if contextEnd > len(jsonString) {
				contextEnd = len(jsonString)
			}

			// Replace newlines with spaces for better readability
			context := strings.ReplaceAll(jsonString[contextStart:contextEnd], "\n", " ")

			// Highlight the match
			fmt.Printf("  %s[%s]%s\n", 
				context[:start-contextStart],
				context[start-contextStart:end-contextStart],
				context[end-contextStart:])
		}
	}
}
