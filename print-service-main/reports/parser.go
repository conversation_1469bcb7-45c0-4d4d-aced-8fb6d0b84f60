package reports

import (
	"encoding/json"
	"fmt"
	"time"
)

// MuitaTretaInput represents the structure of muita_treta.json
type MuitaTretaInput struct {
	CreatedAt         string                   `json:"created_at"`
	UserID            string                   `json:"user_id"`
	ReportName        string                   `json:"report_name"`
	ReportType        string                   `json:"report_type"`
	SubjectMotherName string                   `json:"subject_mother_name"`
	SubjectSex        string                   `json:"subject_sex"`
	OmittedNotes      []interface{}            `json:"omitted_notes"`
	UserReportsID     string                   `json:"user_reports_id"`
	Data              map[string][]SectionData `json:"data"`
}

// SectionData represents a section in the muita_treta.json
type SectionData struct {
	Title      string                   `json:"title"`
	Subtitle   string                   `json:"subtitle"`
	Subsection string                   `json:"subsection"`
	IsDeleted  bool                     `json:"is_deleted"`
	Sources    []string                 `json:"sources"`
	DataCount  int                      `json:"data_count"`
	Data       []map[string]interface{} `json:"data"`
}

// ParseMuitaTreta converts muita_treta.json format to test-report-payload.json format
func ParseMuitaTreta(inputJSON []byte) ([]byte, float64, error) {
	// Start timing for parsing
	startTime := time.Now()

	// Parse the input JSON
	var input MuitaTretaInput
	if err := json.Unmarshal(inputJSON, &input); err != nil {
		return nil, 0, fmt.Errorf("error parsing input JSON: %w", err)
	}

	// Create the output structure
	output := ReportDocumentRequest{
		Sections: []ReportSection{},
		Metadata: ReportMetadata{
			ReportID:          input.UserReportsID,
			ReportStatus:      "completed",
			ReportType:        input.ReportType,
			ReportSearchArgs:  make(map[string]interface{}),
			ReportName:        input.ReportName,
			CreationAt:        input.CreatedAt,
			ModifiedAt:        input.CreatedAt, // Using CreatedAt as ModifiedAt since it's not in the input
			SubjectName:       "",              // Will be populated from data if available
			SubjectMotherName: input.SubjectMotherName,
			SubjectSex:        input.SubjectSex,
		},
	}

	// Process each section in the input data
	for dataType, sections := range input.Data {
		// Add the search argument based on the data type
		if dataType == "cpf" || dataType == "cnpj" || dataType == "email" || dataType == "telefone" {
			// Try to find the value in the first section's data
			if len(sections) > 0 && len(sections[0].Data) > 0 {
				for _, item := range sections[0].Data {
					if details, ok := item["detalhes"].(map[string]interface{}); ok {
						if dataTypeInfo, ok := details[dataType].(map[string]interface{}); ok {
							if value, ok := dataTypeInfo["value"].(string); ok {
								output.Metadata.ReportSearchArgs[dataType] = value
								break
							}
						}
					}
				}
			}
		}

		// Process each section
		for _, section := range sections {
			// Skip deleted sections
			if section.IsDeleted {
				continue
			}

			// Skip subsections
			if section.Subsection != "" {
				continue
			}

			// Transform the data format
			transformedData := make([]map[string]interface{}, 0)
			for _, item := range section.Data {
				// Extract details from the complex structure
				if details, ok := item["detalhes"].(map[string]interface{}); ok {
					flattenedItem := make(map[string]interface{})

					// Extract values from details
					for key, detail := range details {
						if detailMap, ok := detail.(map[string]interface{}); ok {
							if value, exists := detailMap["value"]; exists && !detailMap["is_deleted"].(bool) {
								flattenedItem[key] = value

								// If this is a name field, use it for subject_name in metadata
								if key == "nome_completo" && output.Metadata.SubjectName == "" {
									if name, ok := value.(string); ok {
										output.Metadata.SubjectName = name
									}
								}

								// If this is an age field, use it for subject_age in metadata
								if key == "idade" && output.Metadata.SubjectAge == nil {
									if age, ok := value.(float64); ok {
										ageInt := int(age)
										output.Metadata.SubjectAge = &ageInt
									}
								}
							}
						}
					}

					if len(flattenedItem) > 0 {
						transformedData = append(transformedData, flattenedItem)
					}
				}
			}

			// Create the output section
			outputSection := ReportSection{
				Title:      section.Title,
				Subtitle:   section.Subtitle,
				Subsection: section.Subsection,
				Source:     section.Sources,
				DataCount:  section.DataCount,
				Data:       transformedData,
			}

			output.Sections = append(output.Sections, outputSection)
		}
	}

	// Convert the output to JSON
	outputJSON, err := json.Marshal(output)
	if err != nil {
		return nil, 0, fmt.Errorf("error marshaling output JSON: %w", err)
	}

	// Calculate parsing time in seconds
	parseTime := time.Since(startTime).Seconds()

	return outputJSON, parseTime, nil
}
