{"sections": [{"title": "<PERSON><PERSON>", "subtitle": "", "subsection": "", "source": ["fonte1", "fonte2"], "data_count": 1, "data": [{"nome": "<PERSON>", "cpf": "123.456.789-00", "idade": 35, "estado_civil": "<PERSON><PERSON>iro"}]}, {"title": "Telefones", "subtitle": "", "subsection": "", "source": ["fonte1"], "data_count": 2, "data": [{"numero": "(11) 99999-9999", "tipo": "<PERSON><PERSON><PERSON>", "operadora": "Vivo"}, {"numero": "(11) 3333-3333", "tipo": "Fixo", "operadora": "Telefônica"}]}, {"title": "Emails", "subtitle": "", "subsection": "", "source": ["fonte2"], "data_count": 1, "data": [{"email": "<EMAIL>", "tipo": "Pessoal", "verificado": true}]}, {"title": "Endereços", "subtitle": "", "subsection": "", "source": ["fonte1", "fonte3"], "data_count": 1, "data": [{"logradouro": "<PERSON><PERSON>, 123", "bairro": "Centro", "cidade": "São Paulo", "estado": "SP", "cep": "01234-567"}]}], "metadata": {"report_id": "12345", "report_status": "completed", "report_type": "cpf", "report_search_args": {"cpf": "123.456.789-00"}, "report_name": "Relatório de Pessoa Física", "creation_at": "2024-06-30T10:00:00Z", "modified_at": "2024-06-30T10:30:00Z", "subject_name": "<PERSON>", "subject_mother_name": "<PERSON>", "subject_age": 35, "subject_sex": "M"}}