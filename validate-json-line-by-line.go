package main

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os"
	"strings"
)

func main() {
	// Open the JSON file
	file, err := os.Open("reports-pfd-data.json")
	if err != nil {
		fmt.Printf("Error opening file: %v\n", err)
		os.Exit(1)
	}
	defer file.Close()

	// Read the file line by line
	scanner := bufio.NewScanner(file)
	lineNumber := 0
	jsonString := ""

	for scanner.Scan() {
		lineNumber++
		line := scanner.Text()
		jsonString += line + "\n"

		// Try to parse the JSON accumulated so far
		var result interface{}
		err := json.Unmarshal([]byte(jsonString), &result)

		// If we have a syntax error, print the line number and the error
		if err != nil && strings.Contains(err.Error(), "invalid character") {
			fmt.Printf("Syntax error at line %d: %v\n", lineNumber, err)
			fmt.Printf("Line content: %s\n", line)
			break
		}
	}

	if err := scanner.Err(); err != nil {
		fmt.Printf("Error reading file: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("Validation completed!")
}
