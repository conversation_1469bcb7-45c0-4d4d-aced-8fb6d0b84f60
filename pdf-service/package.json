{"name": "pdf-service", "version": "0.1.0", "main": "dist/index.js", "scripts": {"build": "tsc && npm run copy-assets", "copy-assets": "copyfiles -u 1 \"src/assets/**/*\" dist/", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts"}, "dependencies": {"@react-pdf/renderer": "^3.4.4", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "react": "^18.0.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/react": "^18.0.0", "copyfiles": "^2.4.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.7.3"}}