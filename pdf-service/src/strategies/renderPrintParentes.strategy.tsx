import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '@react-pdf/renderer';
import { ReportSection, ValueWithSource } from '../types/global';
import { translatePropToLabel, getSingular, parseValue, translateSource } from '../helpers';

interface RenderPrintParentesProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      parentesco?: ValueWithSource;
      pessoa?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      detalhes?: Record<string, ValueWithSource>;
      telefones?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      enderecos?: Array<ValueWithSource<Record<string, ValueWithSource>>>;
      [key: string]: any;
    }>
  };
}

export const RenderPrintParentes: React.FC<RenderPrintParentesProps> = ({ section }) => {
  if (!section.data?.length) return null;

  return (
    <View style={styles.container}>
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>

      {section.data.map((parente, parenteIndex) => (
        <View key={`parente-${parenteIndex}`} style={styles.parenteContainer}>
          {/* Parentesco */}
          {parente.parentesco && !parente.parentesco.is_deleted && (
            <View style={styles.parentescoContainer}>
              <View style={styles.parentescoLabelContainer}>
                <Text style={styles.parentescoLabel}>
                  {(translatePropToLabel(parente.parentesco.label || "Parentesco")).toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {parente.parentesco.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <Text style={styles.parentescoValue}>{parseValue(parente.parentesco.value)}</Text>
            </View>
          )}

          {/* Detalhes do Parente */}
          {parente.detalhes && (
            <View style={styles.detalhesContainer}>
              <View style={styles.grid}>
                {Object.entries(parente.detalhes as Record<string, ValueWithSource>)
                  .filter(([_, field]) => !field.is_deleted)
                  .map(([key, field], index) => (
                    <View key={`detalhe-${index}`} style={styles.detailsCell} wrap={false}>
                      <View style={styles.infoContainer}>
                        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                          <Rect width="8" height="8" fill='#CCCCCC' />
                        </Svg>
                        <Text style={styles.label}>{translatePropToLabel(field.label || key).toUpperCase()}</Text>
                        <Text style={styles.sourceText}>
                          {field.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <Text style={styles.value}>{field.value}</Text>
                    </View>
                  ))}
              </View>
            </View>
          )}

          {/* Pessoa relacionada */}
          {parente.pessoa && Array.isArray(parente.pessoa) && parente.pessoa.length > 0 && (
            <View style={styles.pessoaContainer}>
              <View style={styles.subtitleContainer}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill='#889EA3' />
                </Svg>
                <Text style={styles.subtitle}>PESSOA RELACIONADA</Text>
              </View>
              <View style={styles.grid}>
                {parente.pessoa
                  .filter(pessoa => !pessoa.is_deleted)
                  .map((pessoa, pessoaIndex) => (
                    <View key={`pessoa-${pessoaIndex}`} style={styles.participantBlock}>
                      <View style={styles.listContainer} wrap={false}>
                        <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                          <Rect width="4" height="4" fill="#889EA3" />
                        </Svg>
                        <Text style={styles.participantTitle}>
                         {translatePropToLabel(getSingular(pessoa.label) || "Pessoa").toUpperCase()} {pessoaIndex + 1}
                        </Text>
                        <Text style={styles.sourceText}>
                          {pessoa.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <View style={styles.participantFields}>
                        {Object.entries(pessoa.value as Record<string, ValueWithSource<string>>)
                          .filter(([_, field]) => !field.is_deleted)
                          .map(([fieldKey, fieldValue], fieldIndex) => (
                            <View key={`field-${fieldIndex}`} style={styles.pessoaBlock}>
                              <View style={styles.infoContainer} wrap={false}>
                                <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                  <Rect width="8" height="8" fill='#CCCCCC' />
                                </Svg>
                                <Text style={styles.label}>
                                  {translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                </Text>
                                <Text style={styles.sourceText}>
                                  {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                                </Text>
                              </View>
                              <Text style={styles.value}>
                                {String(fieldValue.value || "")}
                              </Text>
                            </View>
                          ))}
                      </View>
                    </View>
                  ))}
              </View>
            </View>
          )}

          {/* Telefones */}
          {parente.telefones && parente.telefones.length > 0 && (
            <View style={styles.telefonesContainer}>
              <View style={styles.subtitleContainer} wrap={false}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill='#889EA3' />
                </Svg>
                <Text style={styles.subtitle}>TELEFONES</Text>
              </View>
              <View style={styles.phoneGrid}>
                {parente.telefones
                  .filter(telefone => !telefone.is_deleted)
                  .map((telefone, index) => (
                    <View key={`telefone-${index}`} style={styles.phoneBlock} >
                      <View style={styles.listContainer} wrap={false}>
                        <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                          <Rect width="4" height="4" fill="#889EA3" />
                        </Svg>
                        <Text style={styles.itemTitle}>
                          {translatePropToLabel(getSingular(telefone.label) || 'TELEFONE').toUpperCase()} {index + 1}
                        </Text>
                        <Text style={styles.sourceText}>
                          {telefone.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <View style={styles.fieldsGrid}>
                        {Object.entries(telefone.value)
                          .filter(([_, field]) => !field.is_deleted)
                          .map(([fieldKey, fieldValue], fieldIndex) => (
                            <View key={`field-${fieldIndex}`} style={styles.cell}>
                              <View style={styles.infoContainer}>
                                <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                  <Rect width="8" height="8" fill='#CCCCCC' />
                                </Svg>
                                <Text style={styles.label}>{translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}</Text>
                                <Text style={styles.sourceText}>
                                  {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                                </Text>
                              </View>
                              <Text style={styles.value}>
                                {String(fieldValue.value || "")}
                              </Text>
                            </View>
                          ))}
                      </View>
                    </View>
                  ))}
              </View>
            </View>
          )}

          {/* Endereços */}
          {parente.enderecos && parente.enderecos.length > 0 && (
            <View style={styles.enderecosContainer}>
              <View style={styles.subtitleContainer}>
                <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                  <Rect width="4" height="4" fill='#889EA3' />
                </Svg>
                <Text style={styles.subtitle}>ENDEREÇOS</Text>
              </View>
              {parente.enderecos
                .filter(endereco => !endereco.is_deleted)
                .map((endereco, index) => (
                  <View key={`endereco-${index}`} style={styles.addressBlock} >
                    <View style={styles.listContainer} wrap={false}>
                      <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                        <Rect width="4" height="4" fill='#889EA3' />
                      </Svg>
                      <Text style={styles.itemTitle}>ENDEREÇO {index + 1}</Text>
                      <Text style={styles.sourceText}>
                        {endereco.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                      </Text>
                    </View>
                    <View style={styles.grid}>
                      {Object.entries(endereco.value)
                        .filter(([_, field]) => !field.is_deleted)
                        .map(([fieldKey, fieldValue], fieldIndex) => (
                          <View key={`field-${fieldIndex}`} style={styles.cell}>
                            <View style={styles.infoContainer} wrap={false}>
                              <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                <Rect width="8" height="8" fill='#CCCCCC' />
                              </Svg>
                              <Text style={styles.label}>
                                {translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                              </Text>
                              <Text style={styles.sourceText}>
                                {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                              </Text>
                            </View>
                            <Text style={styles.value}>
                              {String(fieldValue.value || "")}
                            </Text>
                          </View>
                        ))}
                    </View>
                  </View>
                ))}
            </View>
          )}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: {
    width: 4,
    height: 4,
    marginRight: 4,
    marginTop: 1
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  parentescoLabelContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    paddingBottom: 2,
  },
  subtitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  listContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    borderBottom: '1pt dashed #CCCCCC',
    flexWrap: 'wrap',
  },
  subtitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  parenteContainer: {
    marginBottom: 16,
    padding: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  parentescoContainer: {
    marginBottom: 12,
  },
  parentescoLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FE473C',
  },
  parentescoValue: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  detalhesContainer: {
    marginBottom: 12,
  },
  pessoaContainer: {
    marginBottom: 8,
  },
  telefonesContainer: {
    marginBottom: 8,
  },
  enderecosContainer: {
    marginBottom: 8,
  },
  pessoaBlock: {
    paddingLeft: 8,
    marginBottom: 12,
  },
  participantBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  pessoaCell: {
    paddingRight: 8,
    marginBottom: 10,
  },
  participantTitle: {
    fontSize: 9,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  participantFields: {
    paddingLeft: 8,
  },
  grid: {
    paddingTop: 6,
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailsCell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  cell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
  // Telefone styles
  phoneGrid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  phoneBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  itemTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  fieldsGrid: {
    paddingLeft: 8,
    paddingTop: 6,
  },
  // Endereco styles
  addressBlock: {
    paddingLeft: 8,
    marginBottom: 12,
    borderBottom: '1pt solid #eee',
    paddingBottom: 8,
  },
});