export type ReportType = "cpf" | "cnpj" | "email" | "telefone";

export interface ReportSection {
  title: string;
  subtitle: string;
  subsection: string;
  source: string[];
  data_count: number;
  is_deleted?: boolean;
  data: Array<Record<string, any>>;
}

export interface ReportMetadata {
  [key: string]: any;
  report_id: string;
  report_status: string;
  report_type: string;
  report_search_args: object;
  report_name: string;
  creation_at: string;
  modified_at: string;
  subject_name: string;
  subject_mother_name: string;
  subject_age: number | null;
  subject_sex: string;
}

export interface ValueWithSource<T = any> {
  value: T;
  label: string;
  source: string[];
  is_deleted?: boolean;
}

export interface Dictionary {
  [key: string]: string;
}
