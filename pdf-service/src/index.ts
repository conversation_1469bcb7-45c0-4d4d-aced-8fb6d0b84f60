import express from "express";
import cors from "cors";
import dotenv from 'dotenv';
import path from 'path';
import { errorHandler } from './middlewares/error.middleware';
import pdfRoutes from './routes/pdf.routes';

dotenv.config({ path: path.resolve(__dirname, '../.env') });

const app = express();
const PORT = process.env.PORT || 3002;

// CORS middleware
app.use(cors({
  origin: "*",
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type']
}));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));

// Serve static files from assets directory
app.use('/assets', express.static(path.join(__dirname, 'assets')));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'pdf-service' });
});

// PDF routes
app.use("/pdf", pdfRoutes);

// Error handling middleware
app.use(errorHandler);

app.listen(PORT, () => {
  console.log(`PDF service listening on port ${PORT}`);
});
