{"html": "<!DOCTYPE html> <html lang=\"pt-BR\"> <head> <meta charset=\"UTF-8\" /> <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" /> <title>Gabarito</title> <style> * { padding: 0; margin: 0; box-sizing: border-box; } html { font-size: 10px; } body { font-family: \"Source Sans Pro\", sans-serif; margin: 3rem; } :root { --text: #0b2040; --gray: #717786; --correct: blue; --wrong: red; --white: #fff; } .header { display: flex; justify-content: space-between; align-items: flex-start; } .header__type { font-size: 1.2rem; color: var(--text); font-weight: 400; } .header__title { color: var(--text); font-weight: 700; font-size: 2.4rem; } .header__subtitle { color: var(--gray); font-size: 0.8rem; font-weight: 300; margin-top: 0.3rem; } .result--correct { background-color: green; } .result { display: inline-block; background-color: var(--text); border-radius: 4.5rem; padding: 0.3rem 1rem; margin-top: 0.7rem; } .result__text { color: var(--white); font-weight: 700; font-size: 1rem; } .result--wrong { background-color: var(--wrong); } .header__logo { max-width: 5rem; object-fit: contain; } .infos { margin-top: 2.4rem; display: grid; grid-template-columns: 200px 200px 200px 200px; place-items: end; } .infos__about { font-size: 0.8rem; color: var(--text); font-weight: 400; margin-bottom: 1rem; } .infos__note { font-size: 4.8rem; color: var(--text); font-weight: 400; font-weight: 700; line-height: 3rem; } .infos__item { align-self: end; justify-self: start; } .infos__item-container { margin-bottom: 1rem; } .infos__item-container:last-child { margin-bottom: 0; } .infos__note-text { font-size: 2.4rem; color: var(--text); font-weight: 400; } .infos__title { color: var(--gray); font-weight: 400; font-size: 0.8rem; } .infos__text { color: var(--text); font-weight: 700; font-size: 0.8rem; } .correct-by { display: flex; gap: 0.4rem; align-items: center; margin-top: 3rem; } .correct-by__text { color: var(--gray); font-size: 0.8rem; } .correct-by__bold { font-weight: 700; } .hr { height: 0.05rem; background-color: #dcdde0; margin: 1rem 0; } .question { margin-bottom: 20px; } .question-header { display: flex; justify-content: space-between; align-items: flex-start; } .question-points { font-size: 10px; font-weight: 700; font-family: Source Sans Pro, sans-serif; width: 200px; display: block; } .question-points-incorrect { color: #C92121; } .question-points-correct { color: #39BC3F; } .id { font-size: 6px; color: #717786; font-weight: 300; font-family: Source Sans Pro, sans-serif; } .question-number { font-weight: 600; color: #0B2040; font-size: 14px; } .question-text { color: #0B2040; font-size: 14px; font-family: Source Sans Pro, sans-serif; font-weight: 400; margin: 0; padding: 0; margin-bottom: 20px; max-width: 700px; } .alternative-container { display: flex; align-items: center; gap: 17px; margin-bottom: 8px; page-break-inside: avoid; break-inside: avoid; } .statement { border: 1px solid #717786; padding: 7px; border-radius: 4px; display: flex; gap: 11px; flex-grow: 1; max-width: 470px; } .statement-blank { border: 1px solid #DCDDE0; } .alternative-letter { font-weight: bold; font-family: Source Sans Pro, sans-serif; font-size: 10px; } .alternative-letter-blank { color: #717786; } .alternative-text-blank { color: #717786; } .alternative-text { color: #0B2040; font-weight: 400; font-family: Source Sans Pro, sans-serif; font-size: 10px; } .alternative-feedback { color: #999EAC; font-size: 8px; font-weight: 600; font-family: Source Sans Pro, sans-serif; } .alternative-feedback-correct { color: #39BC3F; } .alternative-feedback-incorrect { color: #C92121; } </style> </head> <body> <header class=\"header\"> <div class=\"header__container\"> <p class=\"header__type\">Gabarito - Questionário</p> <h1 class=\"header__title\"> Terceira atividade bloqueada </h1> <div class=\"result\" style=\"background-color:green;\" > <p class=\"result__text\">Aprovado</p> </div> </div> <picture> <img src=\"https://assets.evob.pro//NJ-jcIO3pSYFzqqyE_r47cmdbos=/filters:format(webp)/https://assets.evob.pro/g22vtob0twik7i03gspjqmres3wd\" alt=\"\" class=\"header__logo\" /> </picture> </header> <section class=\"infos\"> <div class=\"infos__item\"> <p class=\"infos__about\">Sua nota</p> <p class=\"infos__note-text\"><span class=\"infos__note\">0</span>/3.0</p> </div> <div class=\"infos__item\"> <div class=\"infos__item-container\"> <p class=\"infos__title\">Nome</p> <p class=\"infos__text\">Ferjao</p> </div> <div class=\"infos__item-container\"> <p class=\"infos__title\">Data de realização</p> <p class=\"infos__text\">04/03/2024 - 18:10 - 04/03/2024 - 18:20</p> </div> </div> <div class=\"infos__item\"> <div class=\"infos__item-container\"> <p class=\"infos__title\">E-mail</p> <p class=\"infos__text\"><EMAIL></p> </div> <div class=\"infos__item-container\"> <p class=\"infos__title\">Tempo de realização</p> <p class=\"infos__text\">00:10:34</p> </div> </div> <div class=\"infos__item\"> <div class=\"infos__item-container\"> <p class=\"infos__title\">Código da atividade</p> <p class=\"infos__text\">498</p> </div> <div class=\"infos__item-container\"> <p class=\"infos__title\">Total de acertos</p> <p class=\"infos__text\">0/1</p> </div> </div> </section> <section class=\"correct-by\"> <svg width=\"9\" height=\"9\" viewBox=\"0 0 9 9\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"> <path d=\"M4.11092 1.08684H3.41961C1.69132 1.08684 1 1.77816 1 3.50645V5.58039C1 7.30868 1.69132 8 3.41961 8H5.49355C7.22184 8 7.91316 7.30868 7.91316 5.58039V4.88908M5.46245 1.83C5.69404 2.65613 6.34042 3.30251 7.17 3.53756M5.85304 1.43941L3.12925 4.1632C3.02556 4.26689 2.92186 4.47083 2.90112 4.61946L2.75249 5.65989C2.69718 6.03666 2.96334 6.29936 3.34011 6.24751L4.38054 6.09888C4.52571 6.07814 4.72965 5.97444 4.8368 5.87075L7.56059 3.14696C8.03068 2.67687 8.25191 2.13073 7.56059 1.43941C6.86927 0.748095 6.32313 0.969316 5.85304 1.43941Z\" stroke=\"#717786\" stroke-width=\"0.41479\" stroke-miterlimit=\"10\" stroke-linecap=\"round\" stroke-linejoin=\"round\" /> </svg> <p class=\"correct-by__text\"> Corrigido por <span class=\"correct-by__bold\">Automaticamente pelo sistema</span> </p> </section> <div class=\"hr\"></div> <div class=\"question\"> <div class=\"question-header\"> <div> <span class=\"id\"> ID125 </span> <h2 class=\"question-text\"> <span class=\"question-number\"> 1</span> - 3 </h2> </div> <!-- questão incorreta --> <span class=\"question-points question-points-incorrect\"><svg width=\"8\" height=\"7\" viewBox=\"0 0 8 7\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"> <path d=\"M6.28906 6L1.28906 1M1.28906 6L6.28906 1\" stroke=\"#C92121\" stroke-width=\"1.66667\" stroke-linecap=\"round\" stroke-linejoin=\"round\" /> </svg> 0/3.0 pontos</span> </div> <!-- Opção correta não escolhida pelo usuário --> <!-- Não respondido e correta --> <div class=\"alternative-container\"> <svg width=\"12\" height=\"12\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"> <circle cx=\"6\" cy=\"6\" r=\"5.75\" fill=\"white\" stroke=\"#39BC3F\" stroke-width=\"0.5\" /> <path d=\"M3.69238 6.00009L5.22903 7.38471L8.30777 4.61548\" stroke=\"#39BC3F\" stroke-width=\"0.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\" /> </svg> <div class=\"statement statement-blank\"> <span class=\"alternative-letter alternative-letter-blank\">A</span><span class=\"alternative-text alternative-text-blank\">3</span> </div> <span class=\"alternative-feedback\">Resposta Correta</span> </div> <!-- Outras opções --> <!-- Normal --> <div class=\"alternative-container\"> <svg width=\"12\" height=\"12\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"> <circle cx=\"6\" cy=\"6\" r=\"5.75\" fill=\"transparent\" stroke=\"transparent\" stroke-width=\"0.5\" /> <path d=\"M3.69238 6.00009L5.22903 7.38471L8.30777 4.61548\" stroke=\"transparent\" stroke-width=\"0\" stroke-linecap=\"round\" stroke-linejoin=\"round\" /> </svg> <div class=\"statement statement-blank\"> <span class=\"alternative-letter alternative-letter-blank\">B</span><span class=\"alternative-text alternative-text-blank\"> 4</span> </div> </div> </div> </body> </html> ", "config": {"printBackground": true, "marginTop": 0.5, "marginBottom": 2.0, "marginLeft": 0.5, "marginRight": 0.5, "displayHeaderFooter": true, "footerTemplate": "<div style=\"width:100%; display:flex; justify-content:space-between; align-items:center; margin-left:20px; margin-right:20px;\">\n  <span style=\"font-size:10px; color:grey;\">brand blabal</span>\n  <span style=\"font-size:10px; color:grey; margin-left:100px\">Exportado em 11 de 2024</span>\n  <span style=\"font-size:10px; color:grey; margin-right:10px;\"><span class=\"pageNumber\"></span> / <span class=\"totalPages\"></span></span>\n</div>\n"}}