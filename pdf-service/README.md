# PDF Service

A microservice for server-side PDF generation using @react-pdf/renderer.

## Purpose

This service handles PDF generation for large reports to avoid hitting client-side memory limits. It receives report sections and metadata via POST request and returns a PDF stream.

## Setup

```bash
npm install
npm run dev
```

## API

### POST /api/pdf/generate

Generates a PDF from report sections and metadata.

**Request Body:**
```json
{
  "sections": [...],
  "metadata": {...}
}
```

**Response:** PDF file stream

## Environment Variables

- `PORT`: Service port (default: 3002)
- `FRONTEND_URL`: Frontend URL for CORS
- `BFF_URL`: BFF URL for CORS
