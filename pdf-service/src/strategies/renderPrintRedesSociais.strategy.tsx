import React from 'react';
import { View, Text, StyleSheet, Svg, Rect, Image, Link } from '@react-pdf/renderer';
import { ReportSection, ValueWithSource } from '../types/global';
import { translatePropToLabel, translateSource } from '../helpers';
import { isValidUrl, isBase64Image, formatImageSrc } from '../helpers';

interface RenderPrintRedesSociaisProps {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      detalhes: Array<{
        value: {
          [socialMedia: string]: Array<{
            [fieldKey: string]: ValueWithSource;
          }>;
        };
        label: string;
        source: string[];
        is_deleted: boolean;
      }>;
    }>;
  };
}

export const RenderPrintRedesSociais: React.FC<RenderPrintRedesSociaisProps> = ({ section }) => {
  if (!section.data?.length) return null;

  return (
    <View style={styles.container} key={section.title}>
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>

      {section.data.map((dataItem, dataIndex) => (
        <View key={`data-${dataIndex}`} style={styles.socialMediaContainer}>
          {dataItem.detalhes
            .filter(detalhe => !detalhe.is_deleted)
            .map((detalhe, detalheIndex) =>
              Object.entries(detalhe.value).map(([platform, profiles]) => {
                if (!Array.isArray(profiles) || profiles.length === 0) return null;

                const platformName = platform.charAt(0).toUpperCase() + platform.slice(1);

                return (
                  <View key={`platform-${platform}-${detalheIndex}`} style={styles.platformContainer}>
                    <View style={styles.platformTitleContainer}>
                      <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                        <Rect width="4" height="4" fill='#889EA3' />
                      </Svg>
                      <Text style={styles.platformTitle}>{platformName.toUpperCase()}</Text>
                    </View>

                    <View style={styles.profilesGrid}>
                      {profiles.map((profile, profileIndex) => (
                        <View key={`profile-${profileIndex}`} style={styles.profileBlock} >
                          <View style={styles.profileHeaderContainer}>
                            <Svg width={4} height={4} viewBox="0 0 4 4" style={styles.searchIcon}>
                              <Rect width="4" height="4" fill='#889EA3' />
                            </Svg>
                            <Text style={styles.profileTitle}>
                              {platformName.toUpperCase()} {profileIndex + 1}
                            </Text>
                            <Text style={styles.sourceText}>
                              {detalhe.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                            </Text>
                          </View>

                          <View style={styles.fieldsContainer}>
                            {Object.entries(profile)
                              .filter(([_, field]) => !field.is_deleted)
                              .map(([fieldKey, fieldValue], fieldIndex) => {
                                const valueAsString = String(fieldValue.value || "");
                                const isHttpUrl = isValidUrl(valueAsString) && !isBase64Image(valueAsString);
                                const isImageValue = !Array.isArray(fieldValue.value) &&
                                  (isValidUrl(valueAsString) || isBase64Image(valueAsString));

                                return (
                                  <View key={`field-${fieldIndex}`} style={styles.fieldItem}>
                                    <View style={styles.infoContainer}>
                                      <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                                        <Rect width="8" height="8" fill='#CCCCCC' />
                                      </Svg>
                                      <Text style={styles.label}>
                                        {translatePropToLabel(fieldValue.label || fieldKey).toUpperCase()}
                                      </Text>
                                      <Text style={styles.sourceText}>
                                        {fieldValue.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                                      </Text>
                                    </View>

                                    {isImageValue && (
                                      <View style={styles.imageContainer}>
                                        <Image src={formatImageSrc(valueAsString)} style={styles.image} />
                                      </View>
                                    )}

                                    {Array.isArray(fieldValue.value) ? (
                                      <Text style={styles.value}>
                                        {fieldValue.value.map((item: any) => item.rotulo || item).join(', ')}
                                      </Text>
                                    ) : isHttpUrl || isImageValue ? (
                                      <Link src={valueAsString} style={styles.urlText}>
                                        <Text>
                                          {valueAsString.length > 30 ? `${valueAsString.substring(0, 30)}...` : valueAsString}
                                        </Text>
                                      </Link>
                                    ) : (
                                      <Text style={styles.value}>
                                        {valueAsString}
                                      </Text>
                                    )}
                                  </View>
                                );
                              })}
                          </View>
                        </View>
                      ))}
                    </View>
                  </View>
                );
              })
            )}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  imageNotice: {
    fontSize: 9,
    color: '#889EA3',
    fontStyle: 'italic',
  },
  searchIcon: {
    width: 4,
    height: 4,
    marginRight: 4,
    marginTop: 1
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  socialMediaContainer: {
    marginBottom: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  platformContainer: {
    marginBottom: 12,
    padding: 8,
  },
  platformTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    flexWrap: 'wrap',
  },
  platformTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#889EA3',
    textTransform: 'uppercase',
  },
  profilesGrid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  profileBlock: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 10,
  },
  profileHeaderContainer: {
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottom: '1pt dashed #CCCCCC',
    flexWrap: 'wrap',
    marginBottom: 4,
  },
  profileTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  fieldsContainer: {
    paddingLeft: 8,
    paddingTop: 4,
  },
  fieldItem: {
    marginBottom: 6,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  urlText: {
    paddingLeft: 8,
    fontSize: 10,
    maxWidth: '100%',
    width: '100%',
    color: '#000000',
  },
  imageContainer: {
    paddingLeft: 8,
    marginBottom: 4,
    alignItems: 'flex-start',
  },
  image: {
    width: 'auto',
    height: 'auto',
    maxWidth: '100%',
    maxHeight: 150,
    alignSelf: 'flex-start',
  }
});